import { NextRequest, NextResponse } from 'next/server';
import { getDynamicTable, isDrizzleTable, validateDatabaseCode } from '@/lib/drizzleTableMapping';
import { db } from '@/lib/db-server';
import { count, desc, asc, inArray, or, isNull, eq, and } from 'drizzle-orm';

/**
 * 直接数据库搜索API（不依赖Elasticsearch）
 * 专门用于测试和调试多选筛选功能
 */

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    
    // 验证数据库代码
    if (!validateDatabaseCode(database)) {
      return NextResponse.json({
        success: false,
        error: `不支持的数据库代码: ${database}`
      }, { status: 400 });
    }

    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy');
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';
    
    // 获取所有筛选参数
    const filters: Record<string, unknown> = {};
    const processedKeys = new Set<string>();
    
    searchParams.forEach((value, key) => {
      if (!['page', 'limit', 'sortBy', 'sortOrder'].includes(key) && !processedKeys.has(key)) {
        const allValues = searchParams.getAll(key);
        filters[key] = allValues.length === 1 ? allValues[0] : allValues;
        processedKeys.add(key);
      }
    });

    console.log('[DirectSearch] 查询参数:', { 
      database, 
      page, 
      limit, 
      sortBy, 
      sortOrder,
      filters 
    });

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json({
        success: false,
        error: `无效的表对象: ${database}`
      }, { status: 500 });
    }

    // 构建where条件
    const whereConditions = [];
    
    Object.entries(filters).forEach(([key, value]) => {
      if (!value || (Array.isArray(value) && value.length === 0)) return;
      
      const column = table[key];
      if (!column) {
        console.warn(`字段 ${key} 在表中不存在，跳过`);
        return;
      }
      
      if (Array.isArray(value)) {
        // 多选字段处理
        const hasNullValue = value.includes('N/A');
        if (hasNullValue) {
          const nonNullValues = value.filter(v => v !== 'N/A');
          if (nonNullValues.length > 0) {
            whereConditions.push(
              or(
                inArray(column, nonNullValues),
                isNull(column),
                eq(column, '')
              )!
            );
          } else {
            whereConditions.push(
              or(
                isNull(column),
                eq(column, '')
              )!
            );
          }
        } else {
          whereConditions.push(inArray(column, value));
        }
      } else if (value === 'N/A') {
        whereConditions.push(
          or(
            isNull(column),
            eq(column, '')
          )!
        );
      } else {
        whereConditions.push(eq(column, value));
      }
    });

    // 组合where条件
    let finalWhere;
    if (whereConditions.length === 0) {
      finalWhere = undefined;
    } else if (whereConditions.length === 1) {
      finalWhere = whereConditions[0];
    } else {
      finalWhere = and(...whereConditions);
    }

    // 计算总数
    const totalCountResult = await db
      .select({ count: count() })
      .from(table)
      .where(finalWhere);
    
    const totalCount = Number(totalCountResult[0]?.count || 0);

    // 查询数据
    const offset = (page - 1) * limit;
    
    // 构建排序
    let orderByClause = [desc(table.id)]; // 默认按id降序
    if (sortBy && table[sortBy]) {
      const column = table[sortBy];
      orderByClause = sortOrder === 'asc' ? [asc(column)] : [desc(column)];
    }

    let queryBuilder = db
      .select()
      .from(table);

    if (finalWhere) {
      queryBuilder = queryBuilder.where(finalWhere);
    }

    const data = await queryBuilder
      .orderBy(...orderByClause)
      .limit(limit)
      .offset(offset);

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page,
        limit,
        total_pages: Math.ceil(totalCount / limit),
        total_results: totalCount
      },
      search_info: {
        query: '',
        search_time: 0,
        es_took: 0,
        missing_ids_count: 0
      }
    });

  } catch (error) {
    console.error('[DirectSearch] 查询失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '查询失败'
    }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    
    // 验证数据库代码
    if (!validateDatabaseCode(database)) {
      return NextResponse.json({
        success: false,
        error: `不支持的数据库代码: ${database}`
      }, { status: 400 });
    }

    const body = await request.json();
    const { 
      filters = {}, 
      sort_by, 
      sort_order = 'desc',
      page = 1, 
      limit = 20 
    } = body;

    console.log('[DirectSearch] POST请求:', { 
      database, 
      page, 
      limit, 
      sort_by, 
      sort_order,
      filters 
    });

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json({
        success: false,
        error: `无效的表对象: ${database}`
      }, { status: 500 });
    }

    // 构建where条件（与GET方法相同的逻辑）
    const whereConditions = [];
    
    Object.entries(filters).forEach(([key, value]) => {
      if (!value || (Array.isArray(value) && value.length === 0)) return;
      
      const column = table[key];
      if (!column) {
        console.warn(`字段 ${key} 在表中不存在，跳过`);
        return;
      }
      
      if (Array.isArray(value)) {
        const hasNullValue = value.includes('N/A');
        if (hasNullValue) {
          const nonNullValues = value.filter(v => v !== 'N/A');
          if (nonNullValues.length > 0) {
            whereConditions.push(
              or(
                inArray(column, nonNullValues),
                isNull(column),
                eq(column, '')
              )!
            );
          } else {
            whereConditions.push(
              or(
                isNull(column),
                eq(column, '')
              )!
            );
          }
        } else {
          whereConditions.push(inArray(column, value));
        }
      } else if (value === 'N/A') {
        whereConditions.push(
          or(
            isNull(column),
            eq(column, '')
          )!
        );
      } else {
        whereConditions.push(eq(column, value));
      }
    });

    let finalWhere;
    if (whereConditions.length === 0) {
      finalWhere = undefined;
    } else if (whereConditions.length === 1) {
      finalWhere = whereConditions[0];
    } else {
      finalWhere = and(...whereConditions);
    }

    // 计算总数和查询数据（与GET方法相同）
    const totalCountResult = await db
      .select({ count: count() })
      .from(table)
      .where(finalWhere);
    
    const totalCount = Number(totalCountResult[0]?.count || 0);

    const offset = (page - 1) * limit;
    
    let orderByClause = [desc(table.id)];
    if (sort_by && table[sort_by]) {
      const column = table[sort_by];
      orderByClause = sort_order === 'asc' ? [asc(column)] : [desc(column)];
    }

    let queryBuilder = db
      .select()
      .from(table);

    if (finalWhere) {
      queryBuilder = queryBuilder.where(finalWhere);
    }

    const data = await queryBuilder
      .orderBy(...orderByClause)
      .limit(limit)
      .offset(offset);

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page,
        limit,
        total_pages: Math.ceil(totalCount / limit),
        total_results: totalCount
      },
      search_info: {
        query: '',
        search_time: 0,
        es_took: 0,
        missing_ids_count: 0
      }
    });

  } catch (error) {
    console.error('[DirectSearch] POST查询失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'POST查询失败'
    }, { status: 500 });
  }
}
